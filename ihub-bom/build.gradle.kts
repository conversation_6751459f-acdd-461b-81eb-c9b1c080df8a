/*
 * Copyright (c) 2021-2023 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
description = "IHub Bom Gradle Plugins"

dependencies {
    implementation(libs.dependency.management)
}

gradlePlugin {
    plugins {
        create("iHubBom") {
            id = "pub.ihub.plugin.ihub-bom"
            displayName = "IHub Bom"
            description = "IHub Bom Plugin"
            implementationClass = "pub.ihub.plugin.bom.IHubBomPlugin"
            tags.set(listOf("ihub", "java", "bom"))
        }
    }
}
