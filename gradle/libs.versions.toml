[versions]
ihub = '1.6.5'
freefair = '8.14'
spring-boot = '3.5.4'
plugin-publish = '1.3.1'
jacoco = '0.8.13'
codenarc = '3.6.0-groovy-4.0'
pmd = '7.16.0'

[libraries]
dependency-management = { module = 'io.spring.gradle:dependency-management-plugin', version = '1.1.7' }
freefair-utils = { module = 'io.freefair.gradle:plugin-utils', version.ref = 'freefair' }
freefair-git = { module = 'io.freefair.gradle:git-plugin', version.ref = 'freefair' }
freefair-lombok = { module = 'io.freefair.gradle:lombok-plugin', version.ref = 'freefair' }
freefair-github = { module = 'io.freefair.gradle:github-plugin', version.ref = 'freefair' }
freefair-settings = { module = 'io.freefair.gradle:settings-plugin', version.ref = 'freefair' }
ben-manes-versions = { module = 'com.github.ben-manes:gradle-versions-plugin', version = '0.52.0' }
maven-central = { module = 'tech.yanand.gradle:maven-central-publish', version = '1.3.0' }
shadow = { module = 'com.github.johnrengelman:shadow', version = '8.1.1' }
javaagent = { module = 'com.ryandens:plugin', version = '0.9.1' }
byte-buddy = { module = 'net.bytebuddy:byte-buddy-gradle-plugin', version = '1.17.6' }
jmolecules-bytebuddy = { module = 'org.jmolecules.integrations:jmolecules-bytebuddy', version = '1.6.0' }
johnrengelman-processes = { module = 'com.github.johnrengelman.processes:com.github.johnrengelman.processes.gradle.plugin', version = '0.5.0' }
spring-boot = { module = 'org.springframework.boot:spring-boot-gradle-plugin', version.ref = 'spring-boot' }
spring-boot-bp = { module = 'org.springframework.boot:spring-boot-buildpack-platform', version.ref = 'spring-boot' }
native = { module = 'org.graalvm.buildtools:native-gradle-plugin', version = '0.11.0' }
kotlin = { module = 'org.jetbrains.kotlin:kotlin-gradle-plugin', version = '2.2.0' }
toml = { module = 'org.tomlj:tomlj', version = '1.1.1' }

jacoco = { module = 'org.jacoco:jacoco', version.ref = 'jacoco' }
codenarc = { module = 'org.codenarc:CodeNarc', version.ref = 'codenarc' }
pmd = { module = 'net.sourceforge.pmd:pmd', version.ref = 'pmd' }

ihub-dependencies = { module = 'pub.ihub.lib:ihub-dependencies', version.ref = 'ihub' }

gradle-node = { module = 'com.github.node-gradle:gradle-node-plugin', version = '7.1.0' }

[plugins]
testkit = { id = 'pl.droidsonroids.jacoco.testkit', version = '1.0.12' }
plugin-publish = { id = 'com.gradle.plugin-publish', version.ref = 'plugin-publish' }
