#
# Copyright (c) 2021-2023 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
org.gradle.parallel=true
org.gradle.workers.max=6
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.daemon=false
org.gradle.daemon.performance.enable-monitoring=false
org.gradle.configuration-cache.parallel=true

name=ihub-plugins-gradle-plugin
group=pub.ihub.plugin

iHubSettings.skippedDirs=samples
iHubJava.defaultDependencies=false
