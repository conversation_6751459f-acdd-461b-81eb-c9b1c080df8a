/*
 * Copyright (c) 2021 Henry 李恒 (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
plugins {
    id 'pub.ihub.plugin.ihub-bom'
}

description = 'BOM插件DSL扩展配置样例'

iHubBom {
    // 导入mavenBom
    importBoms {
        group 'pub.ihub.lib' module 'ihub-bom' version '1.4.6'
    }
    // 配置依赖默认版本
    dependencyVersions {
        group 'pub.ihub.lib' modules 'ihub-core', 'ihub-process' version '1.4.6'
    }
    // 配置组版本策略
    groupVersions {
        group 'pub.ihub.lib' version '1.4.6'
    }
    // 排除组件依赖
    excludeModules {
        group 'org.slf4j' modules 'slf4j-api'
        // 支持排除整个组
        group 'pub.ihub'
    }
    // 配置组件依赖
    dependencies {
        api 'pub.ihub.lib:ihub-core'
    }
    // 配置组件能力
    capabilities {
        requireCapability 'pub.ihub.lib:ihub-boot-cloud-spring-boot-starter', 'pub.ihub.lib:reactor-support'
        requireCapability 'ihub-boot-cloud-spring-boot-starter', 'nacos-support'
    }
}
