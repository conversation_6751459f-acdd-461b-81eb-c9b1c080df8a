/*
 * Copyright (c) 2021 Henry 李恒 (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
plugins {
    id 'pub.ihub.plugin.ihub-boot'
}

description = '应用模块'

dependencies {
    implementation project(':sample-multi-sdk')
    implementation project(':sample-multi-service')
    implementation 'org.springframework.boot:spring-boot-starter-web'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}

iHubTest {
    classes = '**/*Test*'
}

iHubVerification {
    jacocoInstructionCoveredRatio = '0.6'
    jacocoPackageExclusion = 'pub.ihub.sample'
}
